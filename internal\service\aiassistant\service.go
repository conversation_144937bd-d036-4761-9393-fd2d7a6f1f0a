package aiassistant

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/aiassistant"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/achievement"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/wallet"
	"github.com/dsoplabs/dinbora-backend/internal/service/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/service/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/service/progression"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/dsoplabs/dinbora-backend/internal/service/vault"
)

// Service defines the interface for the AI Assistant service
type Service interface {
	// FetchForUser retrieves all relevant user data for AI consumption
	FetchForUser(ctx context.Context, userID string) (*aiassistant.AIContextDTO, error)
}

// service implements the Service interface
type service struct {
	UserService           user.Service
	DreamboardService     dreamboard.Service
	FinancialDNAService   financialdna.Service
	FinancialSheetService financialsheet.Service
	ProgressionService    progression.Service
	VaultService          vault.Service
	WalletService         wallet.Service
	AchievementService    achievement.Service
	TrailService          trail.Service
	GamificationService   gamification.Service
}

// New creates a new instance of the AI Assistant service
func New(
	userService user.Service,
	dreamboardService dreamboard.Service,
	financialDNAService financialdna.Service,
	financialSheetService financialsheet.Service,
	progressionService progression.Service,
	vaultService vault.Service,
	walletService wallet.Service,
	achievementService achievement.Service,
	trailService trail.Service,
	gamificationService gamification.Service,
) Service {
	return &service{
		UserService:           userService,
		DreamboardService:     dreamboardService,
		FinancialDNAService:   financialDNAService,
		FinancialSheetService: financialSheetService,
		ProgressionService:    progressionService,
		VaultService:          vaultService,
		WalletService:         walletService,
		AchievementService:    achievementService,
		TrailService:          trailService,
		GamificationService:   gamificationService,
	}
}

// FetchForUser retrieves all relevant user data for AI consumption
func (s *service) FetchForUser(ctx context.Context, userID string) (*aiassistant.AIContextDTO, error) {
	// Initialize the result DTO
	result := &aiassistant.AIContextDTO{
		UserID:           userID,
		DataCompleteness: make(map[string]bool),
	}

	return result, nil
}
